'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Toggle mobile menu
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const navLinkClasses = "px-3 py-2 hover:scale-105 transform transition-transform duration-200 ease-in-out text-gray-700 hover:text-purple-800";

  return (
    <nav className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-full max-w-[95%] md:max-w-[800px] lg:max-w-[900px] transition-all duration-300 ease-in-out">
      <div className={`
        mx-auto px-6 py-4 rounded-full flex items-center justify-between
        bg-transparent backdrop-blur-sm border border-gray-200
        ${isScrolled ? 'shadow-md bg-white/80' : ''}
      `}>
        <Link href="/" className="text-2xl font-heading font-bold bg-gradient-to-r from-purple-900 to-purple-700 bg-clip-text text-transparent">
          Crefy Connect
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center justify-center flex-1 space-x-4">
          <a href="https://v0-crefy-connect-documentation.vercel.app/">Docs</a>
        </div>

        {/* Right-aligned buttons for login & sign up */}
        <div className="hidden md:flex items-center space-x-3">
          <Link href="/auth/signup" className={`${navLinkClasses} border border-[#7B1FA2]/30 rounded-2xl px-4 py-2 hover:border-[#4A148C] hover:text-[#4A148C] hover:bg-[#7B1FA2]/5 transition-all duration-300`}>Sign Up</Link>
          <Link href="/auth/signin" className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white px-6 py-2 rounded-2xl hover:shadow-lg hover:shadow-[#4A148C]/25 transition-all duration-300 font-medium">Sign In</Link>
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <button 
            onClick={toggleMenu}
            className="text-gray-700 focus:outline-none"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu (only visible when open) */}
      {isMenuOpen && (
        <div className="md:hidden mt-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-lg p-4 border border-purple-100">
          <div className="flex flex-col space-y-2">
            <a href="https://v0-crefy-connect-documentation.vercel.app/" className={navLinkClasses} onClick={toggleMenu}>Docs</a>
            <Link href="/auth/signup" className={`${navLinkClasses} border border-gray-300 rounded-full px-4 hover:border-purple-800 hover:text-purple-800`} onClick={toggleMenu}>Sign Up</Link>
            <Link href="/auth/signin" className="bg-purple-900 text-white px-4 py-2 rounded-full hover:bg-purple-800 transition-colors" onClick={toggleMenu}>Developer Login</Link>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;